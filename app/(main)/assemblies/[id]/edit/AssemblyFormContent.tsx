'use client';

import React, { useCallback, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON>Lef<PERSON>, Save, Loader2, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';

import { Button } from '@/app/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card';
import { HierarchicalPartsForm } from '@/app/components/forms/HierarchicalPartsForm';
import { useAssemblyForm } from '@/app/contexts/AssemblyFormContext';
import { Assembly } from '@/app/components/tables/AssembliesTable/types';

// SCHEMA ALIGNMENT: Updated all mapping and form logic to use canonical assembly schema from database_schema_updated.md. Legacy/incorrect fields removed or migrated.
// ... existing code ...
// In all mapping logic, use only:
// - partsRequired: [{ partId, quantityRequired, unitOfMeasure }]
// Remove mapping for parts, quantity, part_id.
// ... existing code ...

// Helper to normalize parts for a more stable comparison (moved outside and wrapped in useCallback)
const normalizePartsForComparison = (partsArray: any[]) => {
  if (!Array.isArray(partsArray)) return [];
  return partsArray.map((part, index) => ({
    id: part?._id || part?.id || part?.clientId || `temp-part-${index}`,
    partId: (typeof part?.partId === 'object' ? part?.partId?._id : part?.partId) || '',
    quantityRequired: part?.quantityRequired || 0,
    unitOfMeasure: part?.unitOfMeasure || ''
  })).sort((a, b) => (a.id || '').localeCompare(b.id || ''));
};

/**
 * Assembly form content component - shared between create and edit pages
 */
export default function AssemblyFormContent() {
  const router = useRouter();
  const {
    formData,
    isLoading,
    isSaving,
    isEditing,
    isDirty,
    saveAssembly,
    resetForm,
    setFormData,
    refreshStockData
  } = useAssemblyForm();

  // Memoize formatted parts separately to stabilize its reference
  const memoizedFormattedParts = useMemo(() => {
    if (!formData || !formData.partsRequired) return []; // Ensure formData and partsRequired exist
    console.log('[AssemblyFormContent] Recomputing memoizedFormattedParts. formData.partsRequired:', formData.partsRequired);

    // Use formData.partsRequired and extract stock from populated Part objects
    const partsArray = formData.partsRequired;

    return Array.isArray(partsArray)
      ? partsArray.map((part: any, index: number) => {
          const partRef = part?.partId; // partId might be an object or string from DB

          // Log part structure for debugging
          console.log(`[AssemblyFormContent] Processing part ${index}:`, {
            partId: typeof partRef === 'object' ? partRef?._id : partRef,
            partRef: partRef,
            hasInventory: typeof partRef === 'object' && partRef?.inventory,
            inventoryCurrentStock: typeof partRef === 'object' ? partRef?.inventory?.currentStock : 'N/A'
          });

          // Use the same direct access pattern as BomViewer for consistency
          const stockValue = typeof partRef === 'object' && partRef !== null && !(partRef instanceof String)
            ? (partRef.inventory?.currentStock || 0)
            : 0;

          console.log(`[AssemblyFormContent] Part ${index} stockValue:`, stockValue);

          return {
            ...part, // Spread the original part object first

            // Then, define or override specific fields to ensure correct values and defaults
            id: part?.id || part?._id || part?.clientId || `temp-part-${index}`,
            partId: (typeof partRef === 'object' && partRef !== null && !(partRef instanceof String) ? partRef._id : partRef) || part?.partId || '',
            name: part?.name || (typeof partRef === 'object' ? partRef?.name : null) || 'Unknown Part',
            description: part?.description || (typeof partRef === 'object' ? partRef?.description : null) || '',
            quantityRequired: part?.quantityRequired || 1,
            unitOfMeasure: part?.unitOfMeasure || (typeof partRef === 'object' ? partRef?.unitOfMeasure : null) || 'PCS',
            cost: part?.cost || (typeof partRef === 'object' ? partRef?.cost : null) || 0,
            notes: part?.notes || '',
            currentStock: stockValue,
            isAssembly: part?.isAssembly || (typeof partRef === 'object' ? partRef?.isAssembly : false) || false,
            category: part?.category,
            reorderLevel: part?.reorderLevel,
            supplier: part?.supplier,
            technicalSpecs: part?.technicalSpecs,
            partDisplayIdentifier: part?.partDisplayIdentifier || (typeof partRef === 'object' && partRef !== null && !(partRef instanceof String) ? partRef.partNumber : partRef) || part?.name || '',
            isExpanded: part?.isExpanded === undefined ? true : part.isExpanded,
            children: part?.children || [],

            partId_name: part?.name || (typeof partRef === 'object' ? partRef?.name : null) || 'Unknown Part',
            originalIndex: index,
          };
        })
      : [];
  }, [formData?.partsRequired]); // Only depend on formData.partsRequired

  // Format assembly data for the form - memoized to prevent unnecessary recalculations
  const formattedAssemblyData = useMemo(() => {
    if (!formData) return undefined;
    console.log('[AssemblyFormContent] Recomputing formattedAssemblyData. formData or memoizedFormattedParts changed.');

    const formDataAny = formData as any;
    
    // Ensure status is one of the allowed values
    let status = formData?.status || 'pending_review';
    if (status === 'in_production') {
      status = 'active'; // Map legacy 'in_production' to 'active'
    }

    return {
      name: formData?.name || 'Unnamed Assembly',
      description: formData?.description || '',
      assemblyCode: formData?.assemblyCode || formDataAny?.assembly_id || '',
      status: status,
      version: formData?.version || 1,
      isTopLevel: formData?.isTopLevel !== undefined ? formData.isTopLevel : true,
      manufacturingInstructions: formData?.manufacturingInstructions || '',
      estimatedBuildTime: formData?.estimatedBuildTime || '',
      assembly_stage: formDataAny?.assembly_stage || 'Final Assembly',
      partsRequired: memoizedFormattedParts // Use the memoized parts
    };
  }, [
    formData?.name,
    formData?.description,
    formData?.assemblyCode,
    (formData as any)?.assembly_id,
    formData?.status,
    formData?.version,
    formData?.isTopLevel,
    formData?.manufacturingInstructions,
    formData?.estimatedBuildTime,
    (formData as any)?.assembly_stage,
    memoizedFormattedParts // Dependency: the memoized parts array reference
  ]);

  // Handle save button click
  const handleSave = async () => {
    // Cast to any to handle potential legacy fields
    const formDataAny = formData as any;

    // Basic form validation
    if (!formData.name?.trim()) {
      toast.error('Assembly name is required');
      return;
    }

    // Check for assembly code in either canonical or legacy format
    if (!(formData.assemblyCode?.trim() || formDataAny.assembly_id?.trim())) {
      toast.error('Assembly Code is required');
      return;
    }

    // Validate that assembly has at least one part using canonical partsRequired
    const partsArray = formData.partsRequired;
    if (!partsArray || !Array.isArray(partsArray) || partsArray.length === 0) {
      toast.error('Assembly must have at least one part');
      return;
    }

    const partsToCheck = formData.partsRequired || [];
    const invalidParts = partsToCheck.filter((part: any) => {
      const partRef = part.partId || part.item_id;
      return !partRef || (typeof partRef === 'object' && !partRef._id);
    });

    if (invalidParts.length > 0) {
      toast.error(`${invalidParts.length} part(s) have invalid or missing references`);
      return;
    }

    const success = await saveAssembly();
    if (success) {
      toast.success(`Assembly ${isEditing ? 'updated' : 'created'} successfully`);
      router.push('/assemblies');
    }
  };

  // Handle form submission - memoized to prevent recreation on every render
  const handleFormSubmit = useCallback(async (hierarchicalFormData: any) => {
    console.log('[AssemblyFormContent] handleFormSubmit CALLED with hierarchicalFormData:', JSON.stringify(hierarchicalFormData));
    const currentFormData = formData || {};
    console.log('[AssemblyFormContent] currentFormData (before potential update):', JSON.stringify(currentFormData));

    // Log detailed part data for debugging stock issues
    console.log('[AssemblyFormContent] Detailed parts analysis:');
    if (hierarchicalFormData.partsRequired) {
      hierarchicalFormData.partsRequired.forEach((part: any, index: number) => {
        console.log(`[AssemblyFormContent] HierarchicalFormData Part ${index}:`, {
          partId: part.partId,
          name: part.name,
          currentStock: part.currentStock,
          quantityRequired: part.quantityRequired
        });
      });
    }
    if (currentFormData.partsRequired) {
      currentFormData.partsRequired.forEach((part: any, index: number) => {
        console.log(`[AssemblyFormContent] CurrentFormData Part ${index}:`, {
          partId: typeof part.partId === 'object' ? part.partId._id : part.partId,
          name: part.name,
          currentStock: typeof part.partId === 'object' ? part.partId.inventory?.currentStock : part.currentStock,
          quantityRequired: part.quantityRequired
        });
      });
    }

    const potentialNewData = {
      ...currentFormData,
      name: hierarchicalFormData.name,
      assemblyCode: hierarchicalFormData.assemblyCode,
      description: hierarchicalFormData.description,
      status: hierarchicalFormData.status,
      version: hierarchicalFormData.version,
      isTopLevel: hierarchicalFormData.isTopLevel,
      manufacturingInstructions: hierarchicalFormData.manufacturingInstructions,
      estimatedBuildTime: hierarchicalFormData.estimatedBuildTime,
      partsRequired: hierarchicalFormData.partsRequired
    };
    console.log('[AssemblyFormContent] Potential new data to set (pre-normalization of its parts):', JSON.stringify(potentialNewData));

    const normalizedPotentialNewParts = normalizePartsForComparison(potentialNewData.partsRequired || []);
    const normalizedCurrentParts = normalizePartsForComparison(currentFormData.partsRequired || []);

    console.log('[AssemblyFormContent] Normalized Parts from POTENTIAL NEW data:', JSON.stringify(normalizedPotentialNewParts));
    console.log('[AssemblyFormContent] Normalized Parts from CURRENT formData:', JSON.stringify(normalizedCurrentParts));

    const partsActuallyChanged = JSON.stringify(normalizedPotentialNewParts) !== JSON.stringify(normalizedCurrentParts);
    console.log('[AssemblyFormContent] partsActuallyChanged (normalized comparison):', partsActuallyChanged);

    const scalarsChanged = 
      potentialNewData.name !== currentFormData.name ||
      potentialNewData.assemblyCode !== currentFormData.assemblyCode ||
      potentialNewData.description !== currentFormData.description ||
      potentialNewData.status !== currentFormData.status ||
      potentialNewData.version !== currentFormData.version ||
      potentialNewData.isTopLevel !== currentFormData.isTopLevel ||
      potentialNewData.manufacturingInstructions !== currentFormData.manufacturingInstructions ||
      potentialNewData.estimatedBuildTime !== currentFormData.estimatedBuildTime;
    console.log('[AssemblyFormContent] scalarsChanged:', scalarsChanged);

    const finalHasChanges = scalarsChanged || partsActuallyChanged;
    console.log('[AssemblyFormContent] Overall finalHasChanges:', finalHasChanges);

    if (finalHasChanges) {
      console.log('[AssemblyFormContent] Calling setFormData because finalHasChanges is true.');
      setFormData(potentialNewData);
    } else {
      console.log('[AssemblyFormContent] SKIPPING setFormData because finalHasChanges is false.');
    }
  }, [formData, setFormData]); // normalizePartsForComparison removed from deps as it's defined outside

  // Handle cancel button click
  const handleCancel = () => {
    if (isDirty) {
      if (confirm('You have unsaved changes. Are you sure you want to cancel?')) {
        resetForm();
        router.push('/assemblies');
      }
    } else {
      router.push('/assemblies');
    }
  };

  if (isLoading) {
    return (
      <div className="container py-8 space-y-8">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-yellow-500" />
          <span className="ml-2 text-lg">Loading assembly...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 max-w-5xl">
      <div className="flex justify-between items-center mb-6">
        <Button variant="ghost" onClick={handleCancel} className="text-muted-foreground hover:text-foreground">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Assemblies
        </Button>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={refreshStockData}
            disabled={isLoading || isSaving}
            className="text-blue-600 border-blue-600 hover:bg-blue-50"
            title="Refresh stock data for all parts"
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh Stock
          </Button>
          <Button
            onClick={handleSave}
            disabled={isSaving || isLoading}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            {isSaving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Assembly
              </>
            )}
          </Button>
        </div>
      </div>

      <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm">
        <CardHeader className="pb-4">
          <CardTitle>Assembly Details</CardTitle>
        </CardHeader>
        <CardContent>
          <HierarchicalPartsForm
            initialData={formattedAssemblyData}
            mode={isEditing ? "edit" : "create"}
            onFormSubmit={handleFormSubmit}
          />
        </CardContent>
      </Card>
    </div>
  );
}
