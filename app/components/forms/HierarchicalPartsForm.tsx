'use client';

import { useState, useEffect, useRef, use<PERSON><PERSON>back, useMemo, forwardRef, useImperativeHandle } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { z } from "zod";
import { useForm, useFieldArray, useWatch, Control, FieldArrayWithId, SubmitHandler, UseFormReturn } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { getApiUrl } from "@/app/utils/apiUtils";
import "./HierarchicalPartsForm.css"; // Make sure this CSS file exists and contains styles

import { Button } from "@/app/components/ui/button";
import { ShimmerButton, MagicCard, InteractiveHoverButton } from "@/app/components/ui/magic-ui";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
    FormDescription,
} from "@/app/components/ui/form";
import { Input } from "@/app/components/ui/input";
import { Textarea } from "@/app/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/app/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select";
import { Switch } from "@/app/components/ui/switch";
import {
    ChevronDown,
    ChevronRight,
    Plus,
    Trash2,
    Search,
    Loader2,
    Save,
    FilePlus,
    PlusCircle,
    XCircle,
    Info,
    Package,
    Layers,
    AlertCircle
} from "lucide-react";
import { v4 as uuidv4 } from 'uuid';
import debounce from "lodash/debounce";
import { PartSearch, PartSearchResult } from "@/app/components/search/PartSearch";
import { FormContainer } from "@/app/components/ui/form-container";
import { Alert, AlertDescription, AlertTitle } from "@/app/components/ui/alert";
import { Badge } from "@/app/components/ui/badge";

// Minimal Part interface (if not already globally defined or imported)
// This is for selectedPart in handlePartSelect if it can be a more detailed object than PartSearchResult
interface Part {
    _id?: string;
    partId: string; // This is usually the user-facing SKU or part number
    name: string;
    description?: string;
    partNumber?: string; // Can be same as partId or a more specific manuf. part number
    currentStock?: number;
    inventory?: {
        currentStock?: number;
        minimumStockLevel?: number;
    };
    unitOfMeasure?: string;
    category?: string;
    isAssembly?: boolean;
    // Add other fields that a full "Part" object might have if different from PartSearchResult
}

// Canonical FormPartData interface
interface FormPartData {
    _id?: string; 
    id?: string; // RHF key
    partId: string;
    name: string;
    description?: string;
    quantityRequired: number;
    isExpanded?: boolean;
    partDisplayIdentifier?: string;
    children?: FormPartData[];
    category?: string | null;
    currentStock?: number | null;
    reorderLevel?: number | null;
    minimumStockLevel?: number | null;
    supplier?: string | null;
    technicalSpecs?: string | null;
    unitOfMeasure?: string | null;
    isAssembly?: boolean | null;
    partNumber?: string | null;
    cost?: number | null;
    location?: string | null;
    additionalAttributes?: Record<string, any> | null;
}

// Define the allowed assembly statuses
type AssemblyStatusType = "active" | "pending_review" | "design_phase" | "design_complete" | "obsolete";

// Simplified AssemblyFormData for core functionality
interface AssemblyFormData {
    _id?: string;
    name: string;
    assemblyId: string; 
    description?: string; // Optional
    category?: string | null; // Optional
    status: AssemblyStatusType; // Use the defined type
    version?: number; // Optional
    notes?: string; // Optional
    partsRequired: FormPartData[];
    // Keep Quick Edit specific fields if they are truly on initialData
    assemblyYield?: number;
    estimatedManufacturingTime?: string;
    assembly_stage?: string;
    // Minimal deprecated fields for compatibility if absolutely needed by the type
    assembly_id?: string;
    assembly_name?: string;
}

const defaultValues: AssemblyFormData = {
    _id: undefined,
    name: '',
    assemblyId: '',
    description: '',
    category: null,
    status: 'active', // Ensure this is a valid AssemblyStatusType
    version: 1,
    notes: '',
    partsRequired: [],
    assemblyYield: 0,
    estimatedManufacturingTime: '',
    assembly_stage: '',
    assembly_id: '',
    assembly_name: '',
};

// Update partSchema and all usages to match the canonical interface
const partSchema: z.ZodType<FormPartData> = z.object({
    _id: z.string().optional(),
    id: z.string().optional(), // For RHF key
    partId: z.string().min(1, "Part ID is required"),
    name: z.string().min(1, "Part name is required"),
    description: z.string().optional(),
    quantityRequired: z.number().min(0.000001, "Quantity must be greater than 0"),
    isExpanded: z.boolean().default(true),
    category: z.string().optional().nullable(),
    currentStock: z.number().optional().nullable(), // Allow null
    reorderLevel: z.number().optional().nullable(),
    minimumStockLevel: z.number().optional().nullable(),
    supplier: z.string().optional().nullable(),
    technicalSpecs: z.string().optional().nullable(),
    unitOfMeasure: z.string().optional().nullable(),
    partDisplayIdentifier: z.string().optional(),
    isAssembly: z.boolean().optional().nullable(),
    partNumber: z.string().optional().nullable(),
    cost: z.number().optional().nullable(),
    location: z.string().optional().nullable(),
    additionalAttributes: z.record(z.any()).optional().nullable(),
    children: z.array(z.lazy(() => partSchema)).optional(),
}).refine(data => {
    // Validate that partId is provided when part is not just a placeholder
    if (data.name && data.name.trim() && !data.partId) {
        return false;
    }
    return true;
}, {
    message: "Part ID is required when part name is provided",
    path: ["partId"],
});

// Define the main form validation schema
const hierarchicalFormSchema = z.object({
    assemblyCode: z.string().min(1, { message: "Assembly code is required" })
        .max(50, { message: "Assembly code cannot exceed 50 characters" })
        .refine(val => val.trim().length > 0, { message: "Assembly code cannot be empty" }),
    name: z.string().min(1, { message: "Assembly name is required" })
        .max(100, { message: "Assembly name cannot exceed 100 characters" })
        .refine(val => val.trim().length > 0, { message: "Assembly name cannot be empty" }),
    description: z.string().max(1000, { message: "Description cannot exceed 1000 characters" })
        .optional()
        .transform(val => val?.trim() || undefined),
    status: z.enum(["active", "pending_review", "design_phase", "design_complete", "obsolete"])
        .default("active"),
    productId: z.string().nullable().optional(),
    parentId: z.string().nullable().optional(),
    isTopLevel: z.boolean().default(true),
    version: z.number().min(1, { message: "Version must be at least 1" }).default(1),
    manufacturingInstructions: z.string().nullable().optional(),
    estimatedBuildTime: z.string().nullable().optional(),
    partsRequired: z.array(partSchema).min(1, { message: "Assembly must have at least one part" }),
});

// TypeScript type for the form values
type HierarchicalFormValues = z.infer<typeof hierarchicalFormSchema>;

// TypeScript types for the component props
interface HierarchicalPartsFormProps {
    initialData?: Partial<HierarchicalFormValues> & { _id?: string; parts?: FormPartData[]; assembly_stage?: string }; // Added assembly_stage for backward compatibility
    mode?: "create" | "edit";
    onFormSubmit: (data: HierarchicalFormValues) => Promise<void>; // Updated to pass all form data
}

// RippleButton component for enhanced button interactions
const RippleButton = forwardRef<
  HTMLButtonElement,
  React.ButtonHTMLAttributes<HTMLButtonElement> & { variant?: "default" | "outline" | "destructive" | "ghost" | "link" | "secondary" }
>(({ className, children, variant = "default", ...props }, ref) => {
  const [ripples, setRipples] = useState<{ x: number; y: number; size: number; id: number }[]>([]);
  const nextId = useRef(0);

  const addRipple = (e: React.MouseEvent<HTMLButtonElement>) => {
    const button = e.currentTarget;
    const rect = button.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height) * 1.5;
    const x = e.clientX - rect.left - size / 2;
    const y = e.clientY - rect.top - size / 2;
    
    const newRipple = { x, y, size, id: nextId.current };
    nextId.current += 1;
    
    setRipples([...ripples, newRipple]);
    
    setTimeout(() => {
      setRipples(prevRipples => prevRipples.filter(ripple => ripple.id !== newRipple.id));
    }, 600);
  };

  const buttonClasses = {
    default: "bg-primary text-primary-foreground hover:bg-primary/90",
    outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
    destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
    ghost: "hover:bg-accent hover:text-accent-foreground",
    link: "text-primary underline-offset-4 hover:underline",
    secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
  };

  return (
    <Button
      ref={ref}
      className={`relative overflow-hidden ${buttonClasses[variant]} ${className || ""}`}
      onMouseDown={addRipple}
      variant={variant}
      {...props}
    >
      {ripples.map(ripple => (
        <span
          key={ripple.id}
          className="absolute rounded-full bg-white/20 animate-ripple pointer-events-none"
          style={{
            left: ripple.x,
            top: ripple.y,
            width: ripple.size,
            height: ripple.size,
          }}
        />
      ))}
      {children}
    </Button>
  );
});

RippleButton.displayName = 'RippleButton';

// PartField component definition
interface PartFieldProps {
    field: any; // Use 'any' to avoid linter constraint error and allow rhfId
    nestLevel: number;
    control: Control<HierarchicalFormValues>;
    removeCurrentPart: () => void;
    pathPrefix: string;
    form: UseFormReturn<HierarchicalFormValues>;
}

const PartField: React.FC<PartFieldProps> = ({
    field,
    nestLevel,
    control,
    removeCurrentPart,
    pathPrefix,
    form,
}) => {
    console.log(`[PartField RENDER] Path: ${pathPrefix}, RHF ID: ${field.id}, Name: ${field.name}`);
    console.log(`[PartField Prop Check] field.currentStock for ${field.partId} (RHF ID: ${field.id}):`, field.currentStock);

    const watchedPart = form.watch(pathPrefix as any) as FormPartData | undefined;
    
    console.log(`[PartField Watched Value] form.watch('${pathPrefix}').currentStock for ${field.partId} (RHF ID: ${field.id}):`, watchedPart?.currentStock);

    const { fields: childFields, append: appendChild, remove: removeChild } = useFieldArray({
        control,
        name: `${pathPrefix}.children` as any,
        keyName: "rhfId"
    });

    // State and logic for suggestions
    const [isSuggestionsVisible, setIsSuggestionsVisible] = useState(false);
    const [suggestions, setSuggestions] = useState<PartSearchResult[]>([]);
    const [isSuggestionsLoading, setIsSuggestionsLoading] = useState(false);
    const suggestionTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    const suggestionsContainerRef = useRef<HTMLDivElement>(null);
    const inputRef = useRef<HTMLInputElement>(null);

    const fetchPartSuggestions = useCallback(
        debounce(async (query: string) => {
            if (query.length < 3) {
                setSuggestions([]);
                setIsSuggestionsLoading(false);
                return;
            }
            setIsSuggestionsLoading(true);
            try {
                const searchParams = new URLSearchParams({ search: query, limit: '10' });
                const apiUrl = getApiUrl(`/api/parts/search?${searchParams.toString()}`);
                const response = await fetch(apiUrl);
                if (!response.ok) throw new Error(`Search failed: ${response.statusText || 'Unknown error'}`);
                const data = await response.json() as { data?: PartSearchResult[] };
                if (data.data && data.data.length > 0) {
                    setSuggestions(data.data);
                } else {
                    setSuggestions([]);
                }
            } catch (error) {
                console.error("Error fetching part suggestions:", error);
                setSuggestions([]);
            } finally {
                setIsSuggestionsLoading(false);
            }
        }, 300),
        []
    );

    const localHandlePartInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        const query = e.target.value;
        form.setValue(pathPrefix + '.partDisplayIdentifier' as any, query);

        if (suggestionTimeoutRef.current) clearTimeout(suggestionTimeoutRef.current);
        if (query.length < 3) {
            setIsSuggestionsLoading(false);
            setSuggestions([]);
            setIsSuggestionsVisible(false);
            return;
        }
        setIsSuggestionsVisible(true);
        fetchPartSuggestions(query);
    }, [fetchPartSuggestions, form, pathPrefix]);

    const handlePartSelect = (selectedPart: PartSearchResult | Part) => {
        const currentFormValues = form.getValues(pathPrefix as any) as FormPartData;

        const selectedPartTrueId = (selectedPart as PartSearchResult)._id || (selectedPart as Part)._id;
        // Assuming PartSearchResult uses 'partNumber' as the field corresponding to our 'partId' (user-facing SKU)
        // and '_id' as its own database ID from the search collection.
        const selectedPartUserFacingId = (selectedPart as PartSearchResult).partNumber || (selectedPart as Part).partId;
        const selectedPartCurrentStock = (selectedPart as PartSearchResult).current_stock ?? (selectedPart as Part).currentStock ?? (selectedPart as Part).inventory?.currentStock ?? 0;
        
        let stockToUse = selectedPartCurrentStock;
        
        if (watchedPart && selectedPartUserFacingId && watchedPart.partId === selectedPartUserFacingId && 
            (selectedPartCurrentStock === 0) && 
            (watchedPart.currentStock !== undefined && watchedPart.currentStock !== null && watchedPart.currentStock > 0) 
        ) {
            console.log(`[PartField] handlePartSelect PRESERVING stock for ${selectedPart.name}. Old: ${watchedPart.currentStock}, New Attempted (from selectedPart): ${selectedPartCurrentStock}. Using old stock.`);
            stockToUse = watchedPart.currentStock;
        }

        const partIdToUse = selectedPartUserFacingId || currentFormValues?.partId;
        const nameToUse = selectedPart.name || currentFormValues?.name;
        const descriptionToUse = selectedPart.description || currentFormValues?.description;
        // partNumber in FormPartData can be the same as partIdToUse if not specified otherwise
        const partNumberToUseInForm = selectedPart.partNumber || currentFormValues?.partNumber || partIdToUse;

        const updatedValues = {
            ...currentFormValues,
            _id: currentFormValues?._id || selectedPartTrueId, 
            partId: partIdToUse, // This is the user-facing ID / SKU
            name: nameToUse,
            description: descriptionToUse,
            partNumber: partNumberToUseInForm, // This could be a more specific manufacturer part number
            partDisplayIdentifier: `${nameToUse} (${partNumberToUseInForm || partIdToUse})`,
            currentStock: stockToUse,
            children: (currentFormValues?.partId && partIdToUse !== currentFormValues.partId) ? [] : currentFormValues?.children || [],
            isExpanded: (currentFormValues?.partId && partIdToUse !== currentFormValues.partId) ? true : currentFormValues?.isExpanded || false,
            // id field for RHF is field.rhfId, not part of this data structure
        };

        form.setValue(pathPrefix as any, updatedValues, { shouldValidate: true, shouldDirty: true });

        setIsSuggestionsVisible(false); // Hide suggestions after selection
        // Clear search query if part is selected
        // This depends on how PartQuickSearch handles its internal state vs. form state
        const partSearchQueryPath = `${pathPrefix}.partSearchQuery`;
        if (form.getValues(partSearchQueryPath as any)) {
            form.setValue(partSearchQueryPath as any, "", { shouldDirty: true });
        }

        // Fetch full part details if only partial data was provided (e.g. from search)
        // This might be redundant if selectedPart from search is comprehensive enough
        // Ensure this fetch doesn't overwrite the currentStock we just carefully set.
        /*
        fetchPartDetails(partIdToUse, pathPrefix).then(details => {
            if (details) {
                const currentData = form.getValues(pathPrefix as any) as FormPartData;
        form.setValue(pathPrefix as any, {
                    ...currentData,
                    name: details.name || currentData.name,
                    description: details.description || currentData.description,
                    partNumber: details.partNumber || currentData.partNumber,
                    // IMPORTANT: Do not overwrite currentStock here unless details.inventory.currentStock is more authoritative
                    // currentStock: details.inventory?.currentStock ?? currentData.currentStock,
                    partDisplayIdentifier: `${details.name || currentData.name} (${details.partNumber || currentData.partNumber || currentData.partId})`,
                    unit: details.unit || currentData.unit,
                    // supplier: details.supplier || currentData.supplier, // Example: if supplier is part of details
                    // category: details.category || currentData.category, // Example: if category is part of details
                }, { shouldValidate: true, shouldDirty: true });
            }
        }).catch(error => {
            console.error("[PartField] Error processing part selection:", error);
        });
        */
    };

    useEffect(() => {
        function handleEscKey(event: KeyboardEvent) {
            if (event.key === 'Escape') {
                setSuggestions([]);
                setIsSuggestionsVisible(false);
            }
        }
        
        function handleClickOutside(event: MouseEvent) {
            if (suggestionsContainerRef.current && !suggestionsContainerRef.current.contains(event.target as Node) &&
                inputRef.current && !inputRef.current.contains(event.target as Node)) {
                setSuggestions([]);
                setIsSuggestionsVisible(false);
            }
        }
        
        document.addEventListener('keydown', handleEscKey);
        document.addEventListener('mousedown', handleClickOutside);
        
        return () => {
            document.removeEventListener('keydown', handleEscKey);
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    const LocalSuggestionDropdown = () => {
        if (!isSuggestionsVisible || (suggestions.length === 0 && !isSuggestionsLoading)) {
            return null;
        }
        return (
            <div 
                ref={suggestionsContainerRef} 
                className="absolute z-50 mt-1 w-full bg-card dark:bg-card shadow-lg rounded-md border border-border max-h-60 overflow-y-auto"
                style={{ top: '100%', left: 0, right: 0 }}
            >
                {isSuggestionsLoading ? (
                    <div className="p-3 flex items-center justify-center">
                        <Loader2 className="h-4 w-4 animate-spin mr-2 text-muted-foreground" />
                        <span className="text-sm text-muted-foreground">Searching parts...</span>
                    </div>
                ) : (
                    <ul className="py-1">
                        {suggestions.map((part) => (
                            <li 
                                key={part._id} 
                                className="px-3 py-2 hover:bg-accent dark:hover:bg-accent/20 cursor-pointer transition-colors" 
                                onClick={() => handlePartSelect(part)}
                            >
                                <div className="flex justify-between items-center">
                                    <div>
                                        <div className="font-medium text-foreground">{part.name}</div>
                                        <div className="text-xs text-muted-foreground">ID: {part.partNumber || part._id}</div>
                                    </div>
                                    <div className={`text-xs px-2 py-1 rounded ${part.current_stock ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300' : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'}`}>
                                        Stock: {part.current_stock ?? 0}
                                    </div>
                                </div>
                                {part.description && (
                                    <div className="text-xs text-muted-foreground mt-1 truncate">{part.description}</div>
                                )}
                            </li>
                        ))}
                    </ul>
                )}
            </div>
        );
    };

    const handleAddChildToCurrentPart = () => {
        appendChild({
            id: uuidv4(),
            partId: "", name: "", description: "", quantityRequired: 1,
            isExpanded: true, partDisplayIdentifier: "", children: []
        } as FormPartData);
        form.setValue(pathPrefix + '.isExpanded' as any, true);
    };

    const hasChildren = watchedPart?.children && watchedPart.children.length > 0;
    
    // Log stock value immediately before rendering the main part field JSX
    const stockAtRenderPoint = form.watch(pathPrefix as any)?.currentStock;
    if(watchedPart) {
      console.log(`[PartField] FINAL CHECK BEFORE JSX RENDER (${watchedPart.name}): path: ${pathPrefix}, stockAtRenderPoint: ${stockAtRenderPoint}, watchedPart.currentStock (from top): ${watchedPart.currentStock}`);
    }

    return (
        <div 
            key={field.rhfId} 
            className={`part-item rounded-lg border border-border/40 p-4 mb-3 transition-all ${nestLevel > 0 ? 'ml-6' : ''} ${watchedPart?.isAssembly ? 'bg-blue-50/50 dark:bg-blue-950/20' : 'bg-card'}`}
        >
            <div className="part-header flex flex-col gap-4 md:flex-row md:items-end">
                <div className="flex-1 space-y-4">
                    <div className="flex items-center gap-2">
                        {hasChildren && (
                            <button 
                                type="button" 
                                onClick={() => form.setValue(pathPrefix + '.isExpanded' as any, !watchedPart.isExpanded)} 
                                className="h-6 w-6 flex items-center justify-center rounded-full hover:bg-accent/50 transition-colors"
                            >
                                {watchedPart.isExpanded ? 
                                    <ChevronDown size={18} className="text-muted-foreground" /> : 
                                    <ChevronRight size={18} className="text-muted-foreground" />
                                }
                    </button>
                )}

                        {watchedPart?.isAssembly && (
                            <div className="text-xs font-medium bg-blue-100 dark:bg-blue-900/40 text-blue-700 dark:text-blue-300 px-2 py-0.5 rounded-full flex items-center">
                                <Layers size={12} className="mr-1" />
                                Assembly
                            </div>
                        )}
                        
                        <div className="flex items-center space-x-2">
                            {(() => {
                                if (watchedPart && typeof watchedPart.currentStock === 'number') {
                                    console.log(`[PartField] Badge Check (${watchedPart.name}): Evaluating currentStock = ${watchedPart.currentStock}`);
                                    if (watchedPart.currentStock <= 0) {
                                        console.log(`[PartField] Badge Check (${watchedPart.name}): Rendering OUT OF STOCK because currentStock (${watchedPart.currentStock}) <= 0`);
                                        return <Badge variant="destructive" className="ml-2 text-xs">Out of Stock</Badge>;
                                    }
                                    console.log(`[PartField] Badge Check (${watchedPart.name}): Stock (${watchedPart.currentStock}) > 0. No 'Out of Stock' badge.`);
                                } else if (watchedPart) {
                                    console.log(`[PartField] Badge Check (${watchedPart.name}): currentStock is not a number: ${typeof watchedPart.currentStock}, value: ${watchedPart.currentStock}`);
                                } else {
                                    console.log(`[PartField] Badge Check: No watchedPart for path ${pathPrefix}`);
                                }
                                return null; 
                            })()}
                            {watchedPart && typeof watchedPart.currentStock === 'number' && watchedPart.currentStock > 0 && watchedPart.minimumStockLevel != null && watchedPart.currentStock < watchedPart.minimumStockLevel && (
                               <Badge variant="warning" className="ml-2 text-xs">Low Stock</Badge>
                            )}
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                    control={form.control}
                    name={pathPrefix + '.partDisplayIdentifier' as any}
                    render={({ field: partDisplayField }) => (
                                <FormItem className="relative">
                                    <FormLabel className="text-xs font-medium text-muted-foreground">Part ID or Search</FormLabel>
                            <FormControl>
                                <div className="relative">
                                            <Search className="absolute left-2 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                    <Input
                                        {...partDisplayField}
                                        placeholder="Enter part code or search..."
                                                className="pl-8 bg-background border-input focus-visible:ring-1 focus-visible:ring-ring"
                                        onChange={localHandlePartInputChange}
                                        onFocus={() => setIsSuggestionsVisible(true)}
                                        autoComplete="off"
                                                ref={inputRef}
                                    />
                                    <LocalSuggestionDropdown />
                                </div>
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />

                <FormField
                    control={form.control}
                    name={pathPrefix + '.name' as any}
                    render={({ field: formFieldRender }) => (
                                <FormItem>
                                    <FormLabel className="text-xs font-medium text-muted-foreground">Part Name</FormLabel>
                            <FormControl>
                                <Input
                                    placeholder="Part Name"
                                            {...formFieldRender}
                                            value={formFieldRender.value || ''}
                                    readOnly
                                    className="bg-muted/50 border-border/30 text-sm p-2 rounded-md w-full cursor-not-allowed"
                                />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />
                    </div>
                </div>

                <div className="flex flex-row gap-4 items-end">
                <FormField
                    control={form.control}
                    name={pathPrefix + '.quantityRequired' as any}
                    render={({ field: formFieldRender }) => (
                            <FormItem className="w-24">
                                <FormLabel className="text-xs font-medium text-muted-foreground">Quantity</FormLabel>
                            <FormControl>
                                <Input
                                    {...formFieldRender}
                                    type="number"
                                    min={1}
                                    value={formFieldRender.value || 1}
                                    onChange={e => formFieldRender.onChange(Number(e.target.value))}
                                        className="bg-background border-input focus-visible:ring-1 focus-visible:ring-ring"
                                />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />

                <FormField
                    control={form.control}
                    name={pathPrefix + '.unitOfMeasure' as any}
                    render={({ field: uomField }) => (
                            <FormItem className="w-28">
                                <FormLabel className="text-xs font-medium text-muted-foreground">Unit</FormLabel>
                                <Select
                                    value={uomField.value || 'ea'}
                                    onValueChange={val => uomField.onChange(val)}
                                >
                                    <FormControl>
                                        <SelectTrigger className="bg-background border-input focus-visible:ring-1 focus-visible:ring-ring">
                                        <SelectValue placeholder="Unit" />
                                    </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                        <SelectItem value="ea">Each (ea)</SelectItem>
                                        <SelectItem value="pcs">Pieces (pcs)</SelectItem>
                                        <SelectItem value="kg">Kilogram (kg)</SelectItem>
                                        <SelectItem value="g">Gram (g)</SelectItem>
                                        <SelectItem value="m">Meter (m)</SelectItem>
                                        <SelectItem value="cm">Centimeter (cm)</SelectItem>
                                        <SelectItem value="mm">Millimeter (mm)</SelectItem>
                                        <SelectItem value="l">Liter (l)</SelectItem>
                                        <SelectItem value="ml">Milliliter (ml)</SelectItem>
                                        <SelectItem value="box">Box</SelectItem>
                                        <SelectItem value="set">Set</SelectItem>
                                        <SelectItem value="other">Other</SelectItem>
                                    </SelectContent>
                                </Select>
                            <FormMessage />
                        </FormItem>
                    )}
                />

                    <div className="flex items-end gap-2 mb-[2px]">
                        <button 
                            type="button" 
                            className="h-8 w-8 flex items-center justify-center text-blue-500 hover:bg-blue-100/50 dark:hover:bg-blue-900/30 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-400 transition-colors" 
                            onClick={handleAddChildToCurrentPart}
                            title="Add Child Part"
                        >
                            <PlusCircle size={18} />
                    </button>
                        <RippleButton 
                            type="button" 
                            variant="ghost"
                            className="h-8 w-8 p-0 flex items-center justify-center text-destructive hover:bg-destructive/10 rounded-full" 
                            onClick={removeCurrentPart}
                            title="Remove Part"
                        >
                            <Trash2 size={18} />
                    </RippleButton>
                    </div>
                </div>
            </div>

            {watchedPart?.description && (
                <div className="mt-3 text-sm text-muted-foreground bg-muted/30 p-2 rounded-md">
                    {watchedPart.description}
                </div>
            )}

            {watchedPart?.isExpanded && childFields.length > 0 && (
                <div className="mt-4 pl-2 border-l-2 border-border/50">
                    {childFields.map((childField, childIndex) => (
                <PartField
                    key={childField.rhfId}
                    field={childField}
                    nestLevel={nestLevel + 1}
                    control={control}
                    removeCurrentPart={() => removeChild(childIndex)}
                    pathPrefix={pathPrefix + '.children.' + childIndex}
                    form={form}
                />
            ))}
                </div>
            )}
        </div>
    );
};

export const HierarchicalPartsForm = forwardRef<{
    triggerSubmit: () => Promise<void>;
}, HierarchicalPartsFormProps>(({ initialData, mode = "create", onFormSubmit }, ref) => {
    console.log("HierarchicalPartsForm initialData:", JSON.stringify(initialData, null, 2));
    const router = useRouter();

    const mapPartsForForm = useCallback((parts: FormPartData[] = []): FormPartData[] => {
        return parts.map(p => {
            // Extract stock from the populated Part object in partId (updated approach)
            const partRef = (p as any).partId;
            const stockFromPartId = typeof partRef === 'object' && partRef !== null ? partRef.inventory?.currentStock : null;
            const stockFromPartData = (p as any).partData?.inventory?.currentStock; // Legacy fallback
            const currentStock = stockFromPartId ?? stockFromPartData ?? p.currentStock ?? 0;

            console.log(`[HierarchicalPartsForm] mapPartsForForm - Part: ${p.name}, partRef type: ${typeof partRef}, stockFromPartId: ${stockFromPartId}, final currentStock: ${currentStock}`);

            return {
                ...p,
                id: p.id || uuidv4(),
                currentStock: currentStock,
                partDisplayIdentifier: p.partDisplayIdentifier || (typeof partRef === 'string' ? partRef : partRef?._id) || p.name || "",
                children: p.children ? mapPartsForForm(p.children) : []
            };
        });
    }, []);

    const form = useForm<HierarchicalFormValues>({
        resolver: zodResolver(hierarchicalFormSchema) as any,
        mode: 'onBlur',
        defaultValues: useMemo(() => {
            const baseDefaults = {
                assemblyCode: "",
                name: "",
                description: "",
                status: "active" as HierarchicalFormValues['status'],
                productId: null,
                parentId: null,
                isTopLevel: true,
                version: 1,
                manufacturingInstructions: null,
                estimatedBuildTime: null,
                partsRequired: [],
            };

            if (initialData) {
                let statusValue = initialData.status;
                if (!statusValue && initialData.assembly_stage) {
                    switch (initialData.assembly_stage) {
                        case "Final Assembly": case "FINAL ASSEMBLY": statusValue = "active"; break;
                        case "Sub-Assembly": case "SUB ASSEMBLY": statusValue = "pending_review"; break;
                        default: statusValue = "active";
                    }
                }
                return {
                    ...baseDefaults,
                    assemblyCode: initialData.assemblyCode || "",
                    name: initialData.name || "",
                    description: initialData.description || "",
                    status: statusValue || "active",
                    productId: initialData.productId || null,
                    parentId: initialData.parentId || null,
                    isTopLevel: initialData.isTopLevel === undefined ? true : initialData.isTopLevel,
                    version: initialData.version || 1,
                    manufacturingInstructions: initialData.manufacturingInstructions || null,
                    estimatedBuildTime: initialData.estimatedBuildTime || null,
                    partsRequired: mapPartsForForm(initialData.partsRequired || initialData.parts || []),
                };
            }
            return baseDefaults;
        }, [initialData, mapPartsForForm])
    });

    // Memoize stable dependencies for the reset effect
    const stableInitialAssemblyId = useMemo(() => initialData?._id, [initialData?._id]);
    const stableInitialPartsRequiredString = useMemo(() => JSON.stringify(initialData?.partsRequired || []), [initialData?.partsRequired]);

    useEffect(() => {
        console.log('[HierarchicalPartsForm] Reset Effect. Mode:', mode, 'InitialData ID:', stableInitialAssemblyId, 'Parts JSON:', stableInitialPartsRequiredString);

        if (mode === 'edit') {
            if (initialData) {
                let statusValue = initialData.status;
                if (!statusValue && initialData.assembly_stage) { 
                    switch (initialData.assembly_stage) {
                        case "Final Assembly": case "FINAL ASSEMBLY": statusValue = "active"; break;
                        case "Sub-Assembly": case "SUB ASSEMBLY": statusValue = "pending_review"; break;
                        default: statusValue = "active";
                    }
                }
                
                const dataToReset: AssemblyFormData = {
                    _id: initialData._id,
                    name: initialData.name || '',
                    assemblyId: initialData.assemblyId || '',
                    description: initialData.description || '',
                    category: initialData.category || null,
                    status: (statusValue || initialData.status || 'active') as AssemblyStatusType, // Cast to ensure type
                    version: initialData.version || 1,
                    notes: initialData.notes || '',
                    partsRequired: [], 
                    assemblyYield: initialData.assemblyYield,
                    estimatedManufacturingTime: initialData.estimatedManufacturingTime,
                    assembly_stage: initialData.assembly_stage,
                    assembly_id: initialData.assemblyId, 
                    assembly_name: initialData.name,     
                };
                
                dataToReset.partsRequired = (initialData.partsRequired || []).map(p => {
                    // Extract stock from the populated Part object in partId (updated approach)
                    const partRef = (p as any).partId;
                    const stockFromPartId = typeof partRef === 'object' && partRef !== null ? partRef.inventory?.currentStock : null;
                    const stockFromPartData = (p as any).partData?.inventory?.currentStock; // Legacy fallback
                    const currentStock = stockFromPartId ?? stockFromPartData ?? p.currentStock ?? 0;

                    console.log(`[HierarchicalPartsForm] Reset Effect - Part: ${p.name}, partRef type: ${typeof partRef}, stockFromPartId: ${stockFromPartId}, final currentStock: ${currentStock}`);

                    const children = (p.children || []).map(child => {
                        const childPartRef = (child as any).partId;
                        const childStockFromPartId = typeof childPartRef === 'object' && childPartRef !== null ? childPartRef.inventory?.currentStock : null;
                        const childStockFromPartData = (child as any).partData?.inventory?.currentStock; // Legacy fallback
                        const childCurrentStock = childStockFromPartId ?? childStockFromPartData ?? child.currentStock ?? 0;

                        return {
                            ...child,
                            id: child._id || child.id || uuidv4(), // RHF ID for child
                            currentStock: childCurrentStock
                        };
                    });

                    return {
                        ...p,
                        id: p._id || p.id || uuidv4(), // RHF ID for parent
                        currentStock: currentStock, // Use the extracted stock value
                        children
                    };
                });

                console.log('[HierarchicalPartsForm] partsRequired in dataToReset before reset (brief):', JSON.stringify(dataToReset.partsRequired.map(p => ({ name: p.name, currentStock: p.currentStock, id: p.id, _id: p._id })), null, 2));
                form.reset(dataToReset);
                console.log('[HierarchicalPartsForm] Form has been reset.');
                const valuesAfterReset = form.getValues();
                if (valuesAfterReset.partsRequired && valuesAfterReset.partsRequired.length > 0) {
                    console.log('[HierarchicalPartsForm] form.getValues().partsRequired[0].currentStock after reset:', valuesAfterReset.partsRequired[0].currentStock);
                    console.log('[HierarchicalPartsForm] form.getValues().partsRequired[0] after reset (full):', JSON.stringify(valuesAfterReset.partsRequired[0], null, 2));
                } else {
                    console.log('[HierarchicalPartsForm] form.getValues().partsRequired is empty or undefined after reset.');
                }
                // setSelectedAssemblyId(initialData._id); // This was causing issues
            }
        } else if (mode === 'create') {
            console.log('[HierarchicalPartsForm] Resetting form with defaultValues for create mode.');
            form.reset(defaultValues);
        }
    }, [mode, stableInitialAssemblyId, stableInitialPartsRequiredString, form, initialData]);

    const { fields, append, remove } = useFieldArray({
        control: form.control,
        name: "partsRequired",
        keyName: "rhfId"
    });

    const addRootPart = () => {
        console.log('[HierarchicalPartsForm] addRootPart called at:', new Date().toISOString());
        append({
            // Ensure all fields of FormPartData are provided or are optional
            _id: uuidv4(), // if it's a new part from client side, this is fine
            id: uuidv4(),  // RHF key
            partId: '',
            name: '',
            description: undefined,
            quantityRequired: 1,
            isExpanded: true,
            category: undefined,
            currentStock: undefined, 
            reorderLevel: undefined,
            minimumStockLevel: undefined,
            supplier: undefined,
            technicalSpecs: undefined,
            unitOfMeasure: 'ea',
            partDisplayIdentifier: '',
            isAssembly: false, 
            partNumber: undefined, 
            cost: undefined,
            location: undefined,
            additionalAttributes: undefined,
            children: []
        } as FormPartData); // Explicit cast to ensure it matches
    };

    const onSubmit: SubmitHandler<HierarchicalFormValues> = async (formValues) => {
        // The stock check logic has been removed to allow saving even if parts are out of stock.
        // If you want to show a non-blocking warning, you could re-introduce a modified
        // checkStockRecursive function here that only toasts warnings but doesn't return false or prevent submission.

        await onFormSubmit(formValues);
    };

    useImperativeHandle(ref, () => ({
        triggerSubmit: async () => {
            await form.handleSubmit(onSubmit)();
        }
    }));

    if (form.formState.errors.partsRequired && form.formState.errors.partsRequired.message) {
        toast.error(form.formState.errors.partsRequired.message);
    }
    
    return (
        <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <Card className="shadow-sm border-border/60 overflow-hidden">
                    <CardHeader className="bg-muted/30 pb-4">
                        <CardTitle className="flex items-center gap-2 text-xl">
                            <Package className="h-5 w-5 text-primary" />
                            Parts Required
                        </CardTitle>
                        <CardDescription>
                            Add parts to this assembly and organize them in a hierarchical structure
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="p-6">
                        {fields.length === 0 ? (
                            <div className="text-center py-12 px-4">
                                <div className="bg-muted/30 rounded-full h-16 w-16 flex items-center justify-center mx-auto mb-4">
                                    <Package size={32} className="text-muted-foreground/60" />
                                </div>
                                <h3 className="text-lg font-medium mb-2">No parts added yet</h3>
                                <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                                    Start building your assembly by adding parts. You can search for existing parts or create new ones.
                                </p>
                                <RippleButton 
                                    type="button" 
                                    onClick={addRootPart} 
                                    className="mx-auto bg-primary/90 hover:bg-primary transition-colors"
                                >
                                    <PlusCircle size={18} className="mr-2" /> Add First Part
                                </RippleButton>
                            </div>
                        ) : (
                            <div className="space-y-4">
                                {fields.map((field, index) => {
                                    const pathPrefix = `partsRequired.${index}`;
                                    const watchedPart = form.watch(pathPrefix as any);
                                    // const partDetails = form.getValues(`${pathPrefix}.partDetails`) as Part | undefined; // Removed: partDetails is not in schema this way

                                    // New Log
                                    console.log(`[HierarchicalPartsForm] Rendering part row ${index} (${watchedPart?.name}), watchedPart.currentStock: ${watchedPart?.currentStock}, field.rhfId: ${field.rhfId}, watchedPart._id (DB): ${watchedPart?._id}`);

                                    const hasChildren = watchedPart?.children && watchedPart.children.length > 0;
                                    const isExpanded = watchedPart?.isExpanded; // Simplified: use watchedPart's state

                                    return (
                                    <PartField
                                            key={field.rhfId}
                                            field={field}
                                        nestLevel={0}
                                        control={form.control}
                                        removeCurrentPart={() => remove(index)}
                                            pathPrefix={pathPrefix}
                                        form={form}
                                    />
                                    );
                                })}
                                
                                <div className="flex justify-start pt-2">
                                    <RippleButton 
                                        type="button" 
                                        variant="outline" 
                                        onClick={addRootPart} 
                                        className="mt-2 border-dashed hover:border-solid transition-all"
                                    >
                                        <Plus size={16} className="mr-2" /> Add Another Part
                                    </RippleButton>
                                </div>
                            </div>
                        )}
                    </CardContent>
                    {fields.length > 0 && (
                        <CardFooter className="bg-muted/20 border-t border-border/40 px-6 py-4">
                            <div className="text-sm text-muted-foreground">
                                <span className="font-medium">{fields.length}</span> top-level part{fields.length !== 1 ? 's' : ''} in this assembly
                            </div>
                        </CardFooter>
                    )}
                </Card>

                {form.formState.errors.partsRequired?.message && (
                    <Alert variant="destructive" className="mt-4 animate-in fade-in-50">
                        <XCircle className="h-4 w-4" />
                        <AlertTitle>Parts Error</AlertTitle>
                        <AlertDescription>{form.formState.errors.partsRequired.message}</AlertDescription>
                    </Alert>
                )}
                {form.formState.errors.root?.message && (
                    <Alert variant="destructive" className="mt-4 animate-in fade-in-50">
                        <XCircle className="h-4 w-4" />
                        <AlertTitle>Form Error</AlertTitle>
                        <AlertDescription>{form.formState.errors.root.message}</AlertDescription>
                    </Alert>
                )}
            </form>
        </Form>
    );
});

HierarchicalPartsForm.displayName = 'HierarchicalPartsForm';

// Add CSS for ripple animation
const style = document.createElement('style');
style.textContent = `
@keyframes ripple {
  to {
    transform: scale(4);
    opacity: 0;
  }
}
.animate-ripple {
  animation: ripple 0.6s linear;
}
`;
document.head.appendChild(style);

export default HierarchicalPartsForm;